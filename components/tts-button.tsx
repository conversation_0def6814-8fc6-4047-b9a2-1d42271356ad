import { memo } from 'react';
import { But<PERSON> } from './ui/button';
import { Tooltip, TooltipContent, TooltipTrigger } from './ui/tooltip';
import { SpeakerIcon, LoaderIcon, StopIcon } from './icons';
import { useTTS } from '@/hooks/use-tts';

interface TTSButtonProps {
  text: string;
  className?: string;
}

function PureTTSButton({ text, className }: TTSButtonProps) {
  const { isLoading, isPlaying, speak, stop } = useTTS();

  const handleClick = () => {
    if (isPlaying) {
      stop();
    } else {
      speak(text);
    }
  };

  const getIcon = () => {
    if (isLoading) {
      return <LoaderIcon className="animate-spin" />;
    }
    if (isPlaying) {
      return <StopIcon />;
    }
    return <SpeakerIcon />;
  };

  const getTooltipText = () => {
    if (isLoading) {
      return 'Generating speech...';
    }
    if (isPlaying) {
      return 'Stop speech';
    }
    return 'Read aloud';
  };

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button
          className={`py-1 px-2 h-fit text-muted-foreground ${className || ''}`}
          variant="outline"
          onClick={handleClick}
          disabled={isLoading || !text.trim()}
        >
          {getIcon()}
        </Button>
      </TooltipTrigger>
      <TooltipContent>{getTooltipText()}</TooltipContent>
    </Tooltip>
  );
}

export const TTSButton = memo(PureTTSButton);
