import type { TextPart, UIMessage } from 'ai';
import useSWR, { useSWRConfig } from 'swr';
import { useCopyToClipboard } from 'usehooks-ts';

import type { Vote, SubPrompt } from '@/lib/db/schema';
import { deleteTrailingMessages } from '@/app/(chat)/actions';

import {
  CopyIcon,
  ThumbDownIcon,
  ThumbUpIcon,
  TrashIcon,
  RefreshCcwIcon,
  InfoIcon,
  MessageIcon,
  SpeakerIcon,
  LoaderIcon,
  StopIcon,
} from './icons';
import { Button } from './ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from './ui/tooltip';
import { memo, useState, useEffect } from 'react';
import equal from 'fast-deep-equal';
import { toast } from 'sonner';
import type { UseChatHelpers } from '@ai-sdk/react';
import { chatModels } from '@/lib/ai/models';
import { usePrompts } from '@/hooks/use-prompts';
import { useAppState } from '@/hooks/use-app-state';
import { useTTS } from '@/hooks/use-tts';

interface PureMessageActionsProps {
  chatId: string;
  message: UIMessage;
  vote: Vote | undefined;
  isLoading: boolean;
  allMessages: UIMessage[];
  setMessages: UseChatHelpers['setMessages'];
  reload: UseChatHelpers['reload'];
  onOpenComments?: () => void;
}

// Simple cache to avoid refetching the same sub-prompt names
const subPromptCache = new Map<string, string>();

export function PureMessageActions({
  chatId,
  message,
  vote,
  isLoading,
  allMessages,
  setMessages,
  reload,
  onOpenComments,
}: PureMessageActionsProps) {
  const { mutate } = useSWRConfig();
  const [_, copyToClipboard] = useCopyToClipboard();
  const [isRegenerating, setIsRegenerating] = useState(false);
  const { prompts } = usePrompts();
  const { selectedPrompt } = useAppState();
  const [subPromptName, setSubPromptName] = useState<string | null>(null);
  const [isLoadingSubPrompt, setIsLoadingSubPrompt] = useState(false);
  const {
    isLoading: isTTSLoading,
    isPlaying: isTTSPlaying,
    speak,
    stop,
  } = useTTS();

  // Fetch comment count for this message
  const { data: comments } = useSWR(
    onOpenComments ? `/api/comments?messageId=${message.id}` : null,
    async (url) => {
      const response = await fetch(url);
      if (response.ok) {
        return response.json();
      }
      return [];
    },
  );
  const commentCount = comments?.length || 0;

  // Fetch sub-prompt name when subPromptId is available
  useEffect(() => {
    const annotations = message.annotations?.[0] as any;
    const subPromptId = annotations?.subPromptId;

    if (subPromptId && !subPromptName && !isLoadingSubPrompt) {
      // Check cache first
      const cachedName = subPromptCache.get(subPromptId);
      if (cachedName) {
        setSubPromptName(cachedName);
        return;
      }

      setIsLoadingSubPrompt(true);
      fetch(`/api/sub-prompts/${subPromptId}`)
        .then((res) => {
          if (!res.ok) {
            throw new Error(`HTTP ${res.status}`);
          }
          return res.json();
        })
        .then((data: SubPrompt) => {
          const name = data.name;
          setSubPromptName(name);
          // Cache the result
          subPromptCache.set(subPromptId, name);
        })
        .catch((err) => {
          console.error('Failed to fetch sub-prompt name:', err);
          // Fallback to showing ID
          setSubPromptName(`ID: ${subPromptId}`);
        })
        .finally(() => {
          setIsLoadingSubPrompt(false);
        });
    }
  }, [message.annotations, subPromptName, isLoadingSubPrompt]);

  if (isLoading && !isRegenerating) return null;
  if (message.role === 'user') return null;

  // Helper function to get message metadata for the info tooltip
  const getMessageMetadata = () => {
    const annotations = message.annotations?.[0] as any;
    if (!annotations) return null;

    const modelId = annotations.modelId;
    const promptId = annotations.promptId;
    const subPromptId = annotations.subPromptId;

    // Find model name
    const model = chatModels.find((m) => m.id === modelId);
    const modelName = model?.name || modelId || 'Unknown';

    // Find prompt name
    let promptName = 'Unknown';
    if (promptId) {
      const prompt = prompts.find((p) => p.id === promptId) || selectedPrompt;
      promptName = prompt?.name || 'Unknown';
    }

    // Format creation date
    const createdAt = message.createdAt ? new Date(message.createdAt) : null;
    const formattedDate = createdAt
      ? createdAt.toLocaleString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
        })
      : 'Unknown';

    return {
      createdAt: formattedDate,
      modelName,
      promptName,
      subPromptName: subPromptId
        ? subPromptName ||
          (isLoadingSubPrompt ? 'Loading...' : `ID: ${subPromptId}`)
        : null,
    };
  };

  const metadata = getMessageMetadata();

  return (
    <TooltipProvider delayDuration={0}>
      <div className="flex flex-row gap-2">
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              className="py-1 px-2 h-fit text-muted-foreground"
              variant="outline"
              onClick={async () => {
                const textFromParts = message.parts
                  ?.filter((part): part is TextPart => part.type === 'text')
                  .map((part) => part.text)
                  .join('\n')
                  .trim();

                if (!textFromParts) {
                  toast.error("There's no text to copy!");
                  return;
                }

                await copyToClipboard(textFromParts);
                toast.success('Copied to clipboard!');
              }}
            >
              <CopyIcon />
            </Button>
          </TooltipTrigger>
          <TooltipContent>Copy</TooltipContent>
        </Tooltip>

        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              className="py-1 px-2 h-fit text-muted-foreground"
              variant="outline"
              onClick={() => {
                const textFromParts = message.parts
                  ?.filter((part): part is TextPart => part.type === 'text')
                  .map((part) => part.text)
                  .join('\n')
                  .trim();

                if (!textFromParts) {
                  toast.error("There's no text to read!");
                  return;
                }

                if (isTTSPlaying) {
                  stop();
                } else {
                  speak(textFromParts);
                }
              }}
              disabled={isTTSLoading}
            >
              {isTTSLoading ? (
                <div className="animate-spin">
                  <LoaderIcon />
                </div>
              ) : isTTSPlaying ? (
                <StopIcon />
              ) : (
                <SpeakerIcon />
              )}
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            {isTTSLoading
              ? 'Generating speech...'
              : isTTSPlaying
                ? 'Stop speech'
                : 'Read aloud'}
          </TooltipContent>
        </Tooltip>

        {metadata && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                className="py-1 px-2 h-fit text-muted-foreground"
                variant="outline"
              >
                <InfoIcon />
              </Button>
            </TooltipTrigger>
            <TooltipContent
              className="whitespace-nowrap"
              side="bottom"
              align="start"
            >
              <div className="space-y-1 text-sm">
                <div>
                  <strong>Date:</strong> {metadata.createdAt}
                </div>
                <div>
                  <strong>Model:</strong> {metadata.modelName}
                </div>
                <div>
                  <strong>Prompt:</strong> {metadata.promptName}
                </div>
                {metadata.subPromptName && (
                  <div>
                    <strong>Sub-prompt:</strong> {metadata.subPromptName}
                  </div>
                )}
              </div>
            </TooltipContent>
          </Tooltip>
        )}

        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              data-testid="message-upvote"
              className="py-1 px-2 h-fit text-muted-foreground !pointer-events-auto"
              disabled={vote?.isUpvoted}
              variant="outline"
              onClick={async () => {
                const upvote = fetch('/api/vote', {
                  method: 'PATCH',
                  body: JSON.stringify({
                    chatId,
                    messageId: message.id,
                    type: 'up',
                  }),
                });

                toast.promise(upvote, {
                  loading: 'Upvoting Response...',
                  success: () => {
                    mutate<Array<Vote>>(
                      `/api/vote?chatId=${chatId}`,
                      (currentVotes) => {
                        if (!currentVotes) return [];

                        const votesWithoutCurrent = currentVotes.filter(
                          (vote) => vote.messageId !== message.id,
                        );

                        return [
                          ...votesWithoutCurrent,
                          {
                            chatId,
                            messageId: message.id,
                            isUpvoted: true,
                          },
                        ];
                      },
                      { revalidate: false },
                    );

                    return 'Upvoted Response!';
                  },
                  error: 'Failed to upvote response.',
                });
              }}
            >
              <ThumbUpIcon />
            </Button>
          </TooltipTrigger>
          <TooltipContent>Upvote Response</TooltipContent>
        </Tooltip>

        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              data-testid="message-downvote"
              className="py-1 px-2 h-fit text-muted-foreground !pointer-events-auto"
              variant="outline"
              disabled={vote && !vote.isUpvoted}
              onClick={async () => {
                const downvote = fetch('/api/vote', {
                  method: 'PATCH',
                  body: JSON.stringify({
                    chatId,
                    messageId: message.id,
                    type: 'down',
                  }),
                });

                toast.promise(downvote, {
                  loading: 'Downvoting Response...',
                  success: () => {
                    mutate<Array<Vote>>(
                      `/api/vote?chatId=${chatId}`,
                      (currentVotes) => {
                        if (!currentVotes) return [];

                        const votesWithoutCurrent = currentVotes.filter(
                          (vote) => vote.messageId !== message.id,
                        );

                        return [
                          ...votesWithoutCurrent,
                          {
                            chatId,
                            messageId: message.id,
                            isUpvoted: false,
                          },
                        ];
                      },
                      { revalidate: false },
                    );

                    return 'Downvoted Response!';
                  },
                  error: 'Failed to downvote response.',
                });
              }}
            >
              <ThumbDownIcon />
            </Button>
          </TooltipTrigger>
          <TooltipContent>Downvote Response</TooltipContent>
        </Tooltip>

        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              data-testid="message-regenerate"
              className="py-1 px-2 h-fit text-muted-foreground !pointer-events-auto"
              variant="outline"
              disabled={isLoading || isRegenerating}
              onClick={async () => {
                setIsRegenerating(true);
                const currentMessageIndex = allMessages.findIndex(
                  (m) => m.id === message.id,
                );
                let targetUserMessage: UIMessage | null = null;

                if (currentMessageIndex > -1) {
                  for (let i = currentMessageIndex - 1; i >= 0; i--) {
                    if (allMessages[i].role === 'user') {
                      targetUserMessage = allMessages[i];
                      break;
                    }
                  }
                }

                if (!targetUserMessage) {
                  toast.error(
                    'Could not find a previous user message to regenerate from.',
                  );
                  setIsRegenerating(false);
                  return;
                }

                const finalTargetUserMessage = targetUserMessage;

                const promise = new Promise((resolve, reject) => {
                  (async () => {
                    try {
                      await deleteTrailingMessages({
                        id: finalTargetUserMessage.id,
                      });

                      mutate(
                        `/api/chat/messages?chatId=${chatId}`,
                        (cachedMessages: UIMessage[] | undefined) => {
                          if (!cachedMessages) return [];
                          const userMsgIndex = cachedMessages.findIndex(
                            (m) => m.id === finalTargetUserMessage.id,
                          );
                          if (userMsgIndex === -1) return cachedMessages;
                          return cachedMessages.slice(0, userMsgIndex + 1);
                        },
                        { revalidate: false },
                      );

                      setMessages((currentMsgs) => {
                        const userMsgIndex = currentMsgs.findIndex(
                          (m) => m.id === finalTargetUserMessage.id,
                        );
                        if (userMsgIndex === -1) return currentMsgs;
                        return currentMsgs.slice(0, userMsgIndex + 1);
                      });

                      await reload();
                      resolve('Response regenerated successfully!');
                    } catch (error) {
                      console.error('Regeneration failed:', error);
                      reject(new Error('Failed to regenerate response.'));
                    }
                  })();
                });

                toast.promise(promise, {
                  loading: 'Regenerating Response...',
                  success: (msg) => {
                    setIsRegenerating(false);
                    return msg as string;
                  },
                  error: (err) => {
                    setIsRegenerating(false);
                    return err.message;
                  },
                });
              }}
            >
              <RefreshCcwIcon />
            </Button>
          </TooltipTrigger>
          <TooltipContent>Regenerate Response</TooltipContent>
        </Tooltip>

        {onOpenComments && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                data-testid="message-comments"
                className={`py-1 px-2 h-fit !pointer-events-auto ${
                  commentCount > 0
                    ? 'text-primary bg-yellow-300/10 border-primary/60 hover:bg-primary/15'
                    : 'text-muted-foreground'
                }`}
                variant="outline"
                onClick={onOpenComments}
              >
                <MessageIcon />
                {commentCount > 0 && (
                  <span className="leading-none text-[15px] font-bold">
                    {commentCount}
                  </span>
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              {commentCount > 0
                ? `View ${commentCount} comment${commentCount === 1 ? '' : 's'}`
                : 'Add comment'}
            </TooltipContent>
          </Tooltip>
        )}

        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              data-testid="message-delete"
              className="py-1 px-2 h-fit text-muted-foreground !pointer-events-auto"
              variant="outline"
              onClick={async () => {
                const deleteRequest = fetch(
                  `/api/message?chatId=${chatId}&messageId=${message.id}`,
                  {
                    method: 'DELETE',
                  },
                );

                toast.promise(deleteRequest, {
                  loading: 'Deleting Message...',
                  success: () => {
                    mutate(
                      `/api/chat/messages?chatId=${chatId}`,
                      (messages: UIMessage[] | undefined) => {
                        if (!messages) return [];
                        return messages.filter((msg) => msg.id !== message.id);
                      },
                      { revalidate: false },
                    );

                    if (setMessages) {
                      setMessages((prevMessages) =>
                        prevMessages.filter((msg) => msg.id !== message.id),
                      );
                    }

                    mutate(`/api/vote?chatId=${chatId}`);

                    return 'Message Deleted!';
                  },
                  error: 'Failed to delete message.',
                });
              }}
            >
              <TrashIcon />
            </Button>
          </TooltipTrigger>
          <TooltipContent>Delete Message</TooltipContent>
        </Tooltip>
      </div>
    </TooltipProvider>
  );
}

export const MessageActions = memo(
  PureMessageActions,
  (prevProps, nextProps) => {
    if (prevProps.chatId !== nextProps.chatId) return false;
    if (prevProps.isLoading !== nextProps.isLoading) return false;
    if (prevProps.message.id !== nextProps.message.id) return false;
    if (!equal(prevProps.message.parts, nextProps.message.parts)) return false;
    if (!equal(prevProps.vote, nextProps.vote)) return false;
    if (prevProps.setMessages !== nextProps.setMessages) return false;
    if (prevProps.reload !== nextProps.reload) return false;
    if (!equal(prevProps.allMessages, nextProps.allMessages)) return false;
    if (prevProps.onOpenComments !== nextProps.onOpenComments) return false;

    return true;
  },
);
