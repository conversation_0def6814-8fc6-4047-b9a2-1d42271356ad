import { memo } from 'react';
import { But<PERSON> } from './ui/button';
import { Tooltip, TooltipContent, TooltipTrigger } from './ui/tooltip';
import { VolumeIcon, SpeakerOffIcon } from './icons';
import { useTTS } from '@/hooks/use-tts';

interface AutoTTSToggleProps {
  className?: string;
}

function PureAutoTTSToggle({ className }: AutoTTSToggleProps) {
  const { isAutoTTSEnabled, toggleAutoTTS } = useTTS();

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button
          className={`py-1 px-2 h-fit ${className || ''}`}
          variant={isAutoTTSEnabled ? 'default' : 'outline'}
          onClick={toggleAutoTTS}
        >
          {isAutoTTSEnabled ? <VolumeIcon /> : <SpeakerOffIcon />}
        </Button>
      </TooltipTrigger>
      <TooltipContent>
        {isAutoTTSEnabled ? 'Disable auto-TTS' : 'Enable auto-TTS'}
      </TooltipContent>
    </Tooltip>
  );
}

export const AutoTTSToggle = memo(PureAutoTTSToggle);
