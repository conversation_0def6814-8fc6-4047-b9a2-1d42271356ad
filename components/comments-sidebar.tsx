'use client';

import { useState, useEffect, useRef } from 'react';
import { Button } from './ui/button';
import { CrossIcon, PlusIcon } from './icons';
import { Textarea } from './ui/textarea';
import { Markdown } from './markdown';
import { toast } from 'sonner';
import type { Session } from 'next-auth';
import { mutate } from 'swr';

interface Comment {
  id: string;
  messageId: string;
  userId: string;
  content: string;
  createdAt: string;
  updatedAt: string;
  userEmail: string | null;
  userType: string | null;
}

interface CommentsSidebarProps {
  messageId: string | null;
  isOpen: boolean;
  onClose: () => void;
  session: Session | null;
  selectedText?: string;
}

export function CommentsSidebar({
  messageId,
  isOpen,
  onClose,
  session,
  selectedText = '',
}: CommentsSidebarProps) {
  const [comments, setComments] = useState<Comment[]>([]);
  const [newComment, setNewComment] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-resize textarea
  const adjustHeight = () => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      const newHeight = Math.min(textareaRef.current.scrollHeight, 200);
      textareaRef.current.style.height = `${newHeight}px`;
    }
  };

  // Focus textarea when sidebar opens with a messageId
  useEffect(() => {
    if (isOpen && messageId && textareaRef.current) {
      // Small delay to ensure the sidebar is fully rendered
      setTimeout(() => {
        textareaRef.current?.focus();
        adjustHeight(); // Also adjust height when focusing
      }, 100);
    }
  }, [isOpen, messageId]);

  // Prepopulate comment with selected text
  useEffect(() => {
    if (selectedText && isOpen) {
      const quotedText = selectedText
        .split('\n')
        .map((line) => `> ${line}`)
        .join('\n');
      setNewComment(`${quotedText}\n\n`);
    } else if (isOpen) {
      setNewComment('');
    }
    // Adjust height after content changes
    setTimeout(adjustHeight, 0);
  }, [selectedText, isOpen]);

  // Handle textarea input changes
  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setNewComment(e.target.value);
    adjustHeight();
  };

  // Handle keyboard shortcuts
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
      e.preventDefault();
      handleSubmitComment();
    }
  };

  // Fetch comments when messageId changes
  useEffect(() => {
    if (!messageId || !isOpen) {
      setComments([]);
      return;
    }

    setIsLoading(true);
    fetch(`/api/comments?messageId=${messageId}`)
      .then((res) => {
        if (!res.ok) {
          throw new Error(`HTTP ${res.status}`);
        }
        return res.json();
      })
      .then((data: Comment[]) => {
        setComments(data);
      })
      .catch((error) => {
        console.error('Failed to fetch comments:', error);
        toast.error('Failed to load comments');
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, [messageId, isOpen]);

  const handleSubmitComment = async () => {
    if (!messageId || !newComment.trim() || !session?.user) {
      return;
    }

    if (session.user.type === 'guest') {
      toast.error('Please sign in to add comments');
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await fetch('/api/comments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          messageId,
          content: newComment.trim(),
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const newCommentData = await response.json();

      // Add the new comment to the list with user info
      const commentWithUserInfo = {
        ...newCommentData,
        userEmail: session.user.email,
        userType: session.user.type,
      };

      setComments((prev) => [...prev, commentWithUserInfo]);
      setNewComment('');

      // Reset textarea height after clearing content
      setTimeout(adjustHeight, 0);

      // Update SWR cache for the comment count in MessageActions
      mutate(`/api/comments?messageId=${messageId}`);

      toast.success('Comment added');
    } catch (error) {
      console.error('Failed to create comment:', error);
      toast.error('Failed to add comment');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteComment = async (commentId: string) => {
    try {
      const response = await fetch(`/api/comments?id=${commentId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      setComments((prev) => prev.filter((comment) => comment.id !== commentId));

      // Update SWR cache for the comment count in MessageActions
      if (messageId) {
        mutate(`/api/comments?messageId=${messageId}`);
      }

      toast.success('Comment deleted');
    } catch (error) {
      console.error('Failed to delete comment:', error);
      toast.error('Failed to delete comment');
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getUserDisplayName = (comment: Comment) => {
    if (comment.userEmail) {
      return comment.userEmail.split('@')[0];
    }
    return 'Unknown User';
  };

  if (!isOpen) {
    return null;
  }

  const canAddComments = session?.user && session.user.type !== 'guest';

  return (
    <div className="fixed right-0 top-0 h-full w-96 bg-background border-l border-border shadow-lg z-50 flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <h3 className="text-lg font-semibold">Comments</h3>
        <Button
          variant="ghost"
          size="icon"
          onClick={onClose}
          className="h-8 w-8"
        >
          <CrossIcon size={16} />
        </Button>
      </div>

      {/* Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {!messageId ? (
          <div className="flex-1 flex items-center justify-center text-muted-foreground">
            Select a message to view comments
          </div>
        ) : (
          <>
            {/* Comments list */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="text-muted-foreground">
                    Loading comments...
                  </div>
                </div>
              ) : comments.length === 0 ? (
                <div className="flex items-center justify-center py-8">
                  <div className="text-muted-foreground">No comments yet</div>
                </div>
              ) : (
                comments.map((comment) => (
                  <div
                    key={comment.id}
                    className="bg-muted/50 rounded-lg p-3 space-y-2"
                  >
                    <div className="flex items-center justify-between">
                      <div className="text-sm font-medium">
                        {getUserDisplayName(comment)}
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="text-xs text-muted-foreground">
                          {formatDate(comment.createdAt)}
                        </div>
                        {session?.user?.id === comment.userId && (
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleDeleteComment(comment.id)}
                            className="h-6 w-6 text-muted-foreground hover:text-destructive"
                          >
                            <CrossIcon size={12} />
                          </Button>
                        )}
                      </div>
                    </div>
                    <div className="text-sm">
                      <Markdown>{comment.content}</Markdown>
                    </div>
                  </div>
                ))
              )}
            </div>

            {/* Add comment form */}
            {canAddComments && (
              <div className="border-t border-border p-4 space-y-3">
                <Textarea
                  ref={textareaRef}
                  placeholder="Add a comment..."
                  value={newComment}
                  onChange={handleTextareaChange}
                  onKeyDown={handleKeyDown}
                  className="min-h-[80px] max-h-[200px] resize-none overflow-y-auto"
                  disabled={isSubmitting}
                />
                <div className="flex justify-end">
                  <Button
                    onClick={handleSubmitComment}
                    disabled={!newComment.trim() || isSubmitting}
                    size="sm"
                  >
                    <PlusIcon size={14} />
                    {isSubmitting ? 'Adding...' : 'Add Comment'}
                  </Button>
                </div>
              </div>
            )}

            {!canAddComments && (
              <div className="border-t border-border p-4">
                <div className="text-sm text-muted-foreground text-center">
                  {session?.user
                    ? 'Guest users cannot add comments'
                    : 'Sign in to add comments'}
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}
