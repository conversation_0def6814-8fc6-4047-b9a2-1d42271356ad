'use client';

import { useState } from 'react';

export function useCommentCounts() {
  const [commentCounts, setCommentCounts] = useState<Record<string, number>>(
    {},
  );

  const getCommentCount = (messageId: string) => {
    return commentCounts[messageId] || 0;
  };

  const setCommentCount = (messageId: string, count: number) => {
    setCommentCounts((prev) => ({
      ...prev,
      [messageId]: Math.max(0, count),
    }));
  };

  const updateCommentCount = (messageId: string, delta: number) => {
    setCommentCounts((prev) => ({
      ...prev,
      [messageId]: Math.max(0, (prev[messageId] || 0) + delta),
    }));
  };

  return {
    commentCounts,
    getCommentCount,
    setCommentCount,
    updateCommentCount,
  };
}

export function useCommentsState() {
  const [selectedMessageId, setSelectedMessageId] = useState<string | null>(
    null,
  );
  const [isCommentsSidebarOpen, setIsCommentsSidebarOpen] = useState(false);
  const [selectedText, setSelectedText] = useState<string>('');

  const openCommentsForMessage = (messageId: string, selectedText?: string) => {
    setSelectedMessageId(messageId);
    setSelectedText(selectedText || '');
    setIsCommentsSidebarOpen(true);
  };

  const closeCommentsSidebar = () => {
    setIsCommentsSidebarOpen(false);
    // Don't clear selectedMessageId immediately to allow for smooth closing animation
    setTimeout(() => {
      if (!isCommentsSidebarOpen) {
        setSelectedMessageId(null);
        setSelectedText('');
      }
    }, 300);
  };

  return {
    selectedMessageId,
    isCommentsSidebarOpen,
    selectedText,
    openCommentsForMessage,
    closeCommentsSidebar,
  };
}
