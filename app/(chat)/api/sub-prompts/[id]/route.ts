import { auth } from '@/app/(auth)/auth';
import { getSubPromptById } from '@/lib/db/queries';
import { NextResponse } from 'next/server';

export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> },
) {
  const session = await auth();

  if (!session?.user) {
    return new Response('Unauthorized', { status: 401 });
  }

  const { id } = await params;

  if (!id) {
    return NextResponse.json(
      { error: 'Sub-prompt ID is required' },
      { status: 400 },
    );
  }

  try {
    const subPrompt = await getSubPromptById({ id });

    if (!subPrompt) {
      return NextResponse.json(
        { error: 'Sub-prompt not found' },
        { status: 404 },
      );
    }

    return NextResponse.json(subPrompt);
  } catch (error) {
    console.error('Failed to fetch sub-prompt:', error);
    return NextResponse.json(
      { error: 'Failed to fetch sub-prompt' },
      { status: 500 },
    );
  }
}
