import {
  appendClientMessage,
  appendResponseMessages,
  createDataStream,
  generateText,
  smoothStream,
  streamText,
} from 'ai';
import { auth } from '@/app/(auth)/auth';
import type { RequestHints } from '@/lib/ai/prompts';
import {
  createStreamId,
  deleteChatById,
  getChatById,
  getMessageCountByUserId,
  getMessagesByChatId,
  getPromptById,
  getSubPromptsByParentId,
  getSubPromptById,
  getStreamIdsByChatId,
  saveChat,
  saveMessages,
} from '@/lib/db/queries';
import {
  generateUUID,
  getTrailingMessageId,
  convertDbMessagesToUiMessages,
} from '@/lib/utils';
import { generateTitleFromUserMessage } from '../../actions';
import {
  substitutePromptVariables,
  parsePromptVariables,
} from '@/lib/utils/prompt-variables';
// import { createDocument } from '@/lib/ai/tools/create-document';
// import { updateDocument } from '@/lib/ai/tools/update-document';
// import { requestSuggestions } from '@/lib/ai/tools/request-suggestions';
// import { getWeather } from '@/lib/ai/tools/get-weather';
import { isProductionEnvironment } from '@/lib/constants';
import { myProvider } from '@/lib/ai/providers';
import { entitlementsByUserType } from '@/lib/ai/entitlements';
import { postRequestBodySchema, type PostRequestBody } from './schema';
import { geolocation } from '@vercel/functions';
import {
  createResumableStreamContext,
  type ResumableStreamContext,
} from 'resumable-stream';
import { after } from 'next/server';
import type { Chat, UserType } from '@/lib/db/schema';
import type { AnthropicProviderOptions } from '@ai-sdk/anthropic';
import type { GoogleGenerativeAIProviderOptions } from '@ai-sdk/google';

export const maxDuration = 60;

let globalStreamContext: ResumableStreamContext | null = null;

function getStreamContext() {
  if (!globalStreamContext) {
    try {
      globalStreamContext = createResumableStreamContext({
        waitUntil: after,
      });
    } catch (error: any) {
      if (error.message.includes('REDIS_URL')) {
        console.log(
          ' > Resumable streams are disabled due to missing REDIS_URL',
        );
      } else {
        console.error(error);
      }
    }
  }

  return globalStreamContext;
}

export async function POST(request: Request) {
  let requestBody: PostRequestBody;

  try {
    const json = await request.json();
    requestBody = postRequestBodySchema.parse(json);
  } catch (error) {
    console.error(error);
    return new Response('Invalid request body', { status: 400 });
  }

  try {
    const { id, message, promptId, selectedChatModel, selectedVisibilityType } =
      requestBody;

    const session = await auth();

    if (!session?.user) {
      return new Response('Unauthorized', { status: 401 });
    }

    const userType: UserType = session.user.type;

    const messageCount = await getMessageCountByUserId({
      id: session.user.id,
      differenceInHours: 24,
    });

    if (messageCount > entitlementsByUserType[userType].maxMessagesPerDay) {
      return new Response(
        'You have exceeded your maximum number of messages for the day! Please try again later.',
        {
          status: 429,
        },
      );
    }

    const chat = await getChatById({ id });

    if (!chat) {
      const title = await generateTitleFromUserMessage({
        message,
      });

      await saveChat({
        id,
        userId: session.user.id,
        title,
        visibility: selectedVisibilityType,
      });
    } else {
      if (chat.userId !== session.user.id) {
        return new Response('Forbidden', { status: 403 });
      }
    }

    const previousMessagesFromDb = await getMessagesByChatId({ id });
    const previousMessages = convertDbMessagesToUiMessages(
      previousMessagesFromDb,
    );

    const messages = appendClientMessage({
      messages: previousMessages,
      message,
    });

    const { longitude, latitude, city, country } = geolocation(request);

    const requestHints: RequestHints = {
      longitude,
      latitude,
      city,
      country,
    };

    await saveMessages({
      messages: [
        {
          chatId: id,
          id: message.id,
          modelId: selectedChatModel,
          promptId: promptId,
          subPromptId: null, // User message doesn't have sub-prompt yet
          role: 'user',
          parts: message.parts,
          attachments: message.experimental_attachments ?? [],
          createdAt: new Date(),
        },
      ],
    });

    const systemPrompt = await getPromptById({ id: promptId });

    // Parse prompt variables for substitution
    const promptVariables = parsePromptVariables(systemPrompt.variables);

    // Check if this prompt has sub-prompts
    const subPrompts = await getSubPromptsByParentId({ parentId: promptId });

    let finalSystemPromptContent = systemPrompt.content;
    let selectedSubPromptId: string | null = null;

    // If there are sub-prompts, use two-step LLM process
    if (subPrompts.length > 0) {
      // Step 1: Create a simplified prompt to select the best sub-prompt
      // Use the main prompt content WITHOUT variable substitution for selection
      const subPromptSelectionPrompt = `${finalSystemPromptContent}

OBECNA INSTRUKCJA:

Na podstawie wiadomości użytkownika, historii rozmowy oraz powyższych instrukcji zdecyduj, na jakim etapie znajduje się obecnie rozmowa i wybierz identyfikator najbardziej odpowiedniego pod-promptu spośród następujących opcji:

${subPrompts.map((sp) => `- ID: ${sp.id}, Name: ${sp.name}`).join('\n')}

Odpowiedz WYŁĄCZNIE identyfikatorem wybranego pod-promptu, niczym więcej.`;

      console.debug(
        '[Debug] Sub-prompt selection prompt:',
        subPromptSelectionPrompt,
      );
      try {
        // First LLM call to select sub-prompt
        const selectionResult = await generateText({
          model: myProvider.languageModel('grok-2'),
          messages: [
            ...messages,
            {
              role: 'user',
              content: subPromptSelectionPrompt,
            },
          ],
          maxTokens: 100, // Keep it short
        });

        const selectedSubPromptIdFromLLM = selectionResult.text.trim();

        console.debug(
          '[Debug] Selected sub-prompt ID:',
          selectedSubPromptIdFromLLM,
        );

        // Get the selected sub-prompt content
        const selectedSubPrompt = await getSubPromptById({
          id: selectedSubPromptIdFromLLM,
        });

        console.debug(
          '[Debug] Selected sub-prompt:',
          selectedSubPromptIdFromLLM,
          selectedSubPrompt.name,
        );

        if (selectedSubPrompt) {
          selectedSubPromptId = selectedSubPromptIdFromLLM;
          finalSystemPromptContent = selectedSubPrompt.content;
        }
      } catch (error) {
        console.warn('Failed to select sub-prompt, using default:', error);
        // Fall back to first sub-prompt if selection fails
        if (subPrompts.length > 0) {
          selectedSubPromptId = subPrompts[0].id;
          finalSystemPromptContent = subPrompts[0].content;
        }
      }
    }

    // Apply variable substitution to the final prompt content
    // This will apply to sub-prompt content if sub-prompts exist, or main prompt if no sub-prompts
    finalSystemPromptContent = substitutePromptVariables(
      finalSystemPromptContent,
      promptVariables,
    );

    console.debug(
      '[Debug] Final system prompt content:',
      finalSystemPromptContent,
    );

    const streamId = generateUUID();
    await createStreamId({ streamId, chatId: id });

    const stream = createDataStream({
      execute: (dataStream) => {
        const result = streamText({
          model: myProvider.languageModel(selectedChatModel),
          system: finalSystemPromptContent,
          messages: [
            ...messages,
            {
              role: 'user',
              content: finalSystemPromptContent,
            },
          ],
          maxSteps: 5,
          providerOptions: {
            ...(selectedChatModel === 'claude-3-7-sonnet' ||
            selectedChatModel === 'claude-sonnet-4'
              ? {
                  anthropic: {
                    thinking: {
                      type: 'enabled',
                      budgetTokens: 2048,
                    },
                  } satisfies AnthropicProviderOptions,
                }
              : {}),
            ...(selectedChatModel === 'gemini-2.5-pro'
              ? {
                  google: {
                    thinkingConfig: {
                      thinkingBudget: 1024,
                      includeThoughts: true,
                    },
                  } satisfies GoogleGenerativeAIProviderOptions,
                }
              : {}),
          },
          // experimental_activeTools:
          //   selectedChatModel === 'grok-3'
          //     ? []
          //     : [
          //         'getWeather',
          //         'createDocument',
          //         'updateDocument',
          //         'requestSuggestions',
          //       ],
          experimental_transform: smoothStream({ chunking: 'word' }),
          experimental_generateMessageId: generateUUID,
          // tools: {
          //   getWeather,
          //   createDocument: createDocument({ session, dataStream }),
          //   updateDocument: updateDocument({ session, dataStream }),
          //   requestSuggestions: requestSuggestions({
          //     session,
          //     dataStream,
          //   }),
          // },
          onFinish: async ({ response }) => {
            if (session.user?.id) {
              try {
                const assistantId = getTrailingMessageId({
                  messages: response.messages.filter(
                    (message) => message.role === 'assistant',
                  ),
                });

                if (!assistantId) {
                  throw new Error('No assistant message found!');
                }

                const [, assistantMessage] = appendResponseMessages({
                  messages: [message],
                  responseMessages: response.messages,
                });

                await saveMessages({
                  messages: [
                    {
                      id: assistantId,
                      chatId: id,
                      promptId: promptId,
                      subPromptId: selectedSubPromptId,
                      modelId: selectedChatModel,
                      role: assistantMessage.role,
                      parts: assistantMessage.parts,
                      attachments:
                        assistantMessage.experimental_attachments ?? [],
                      createdAt: new Date(),
                    },
                  ],
                });
              } catch (_) {
                console.error('Failed to save chat');
              }
            }
          },
          experimental_telemetry: {
            isEnabled: isProductionEnvironment,
            functionId: 'stream-text',
          },
        });

        result.consumeStream();

        result.mergeIntoDataStream(dataStream, {
          sendReasoning: true,
        });
      },
      onError: (error) => {
        console.error(error);
        return 'Oops, an error occurred!';
      },
    });

    const streamContext = getStreamContext();

    if (streamContext) {
      return new Response(
        await streamContext.resumableStream(streamId, () => stream),
      );
    } else {
      return new Response(stream);
    }
  } catch (error) {
    console.error(error);
    return new Response('An error occurred while processing your request!', {
      status: 500,
    });
  }
}

export async function GET(request: Request) {
  const streamContext = getStreamContext();

  if (!streamContext) {
    return new Response(null, { status: 204 });
  }

  const { searchParams } = new URL(request.url);
  const chatId = searchParams.get('chatId');

  if (!chatId) {
    return new Response('id is required', { status: 400 });
  }

  const session = await auth();

  if (!session?.user) {
    return new Response('Unauthorized', { status: 401 });
  }

  let chat: Chat;

  try {
    chat = await getChatById({ id: chatId });
  } catch {
    return new Response('Not found', { status: 404 });
  }

  if (!chat) {
    return new Response('Not found', { status: 404 });
  }

  if (chat.visibility === 'private' && chat.userId !== session.user.id) {
    return new Response('Forbidden', { status: 403 });
  }

  const streamIds = await getStreamIdsByChatId({ chatId });

  if (!streamIds.length) {
    return new Response('No streams found', { status: 404 });
  }

  const recentStreamId = streamIds.at(-1);

  if (!recentStreamId) {
    return new Response('No recent stream found', { status: 404 });
  }

  const emptyDataStream = createDataStream({
    execute: () => {},
  });

  return new Response(
    await streamContext.resumableStream(recentStreamId, () => emptyDataStream),
    {
      status: 200,
    },
  );
}

export async function DELETE(request: Request) {
  const { searchParams } = new URL(request.url);
  const id = searchParams.get('id');

  if (!id) {
    return new Response('Not Found', { status: 404 });
  }

  const session = await auth();

  if (!session?.user?.id) {
    return new Response('Unauthorized', { status: 401 });
  }

  try {
    const chat = await getChatById({ id });

    if (chat.userId !== session.user.id) {
      return new Response('Forbidden', { status: 403 });
    }

    const deletedChat = await deleteChatById({ id });

    return Response.json(deletedChat, { status: 200 });
  } catch (error) {
    console.error(error);
    return new Response('An error occurred while processing your request!', {
      status: 500,
    });
  }
}
